# Test info

- Name: Comment Highlighting and Selection Synchronization >> should synchronize selection between editor and sidebar
- Location: /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/comment-highlighting.integration.test.ts:73:3

# Error details

```
TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('button:has-text("New Document")') to be visible

    at /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/comment-highlighting.integration.test.ts:20:16
```

# Page snapshot

```yaml
- banner:
  - heading "Collaborative Editor" [level=2]
- main:
  - heading "Collaborative Editor" [level=1]
  - paragraph: Create and edit documents together in real-time
  - textbox "Email": <EMAIL>
  - textbox "Password": password123
  - button "Sign in"
  - text: Don't have an account?
  - button "Sign up instead"
  - separator
  - text: or
  - separator
  - button "Sign in anonymously"
- region "Notifications alt+T"
```

# Test source

```ts
   1 | /**
   2 |  * Integration tests for comment highlighting and selection synchronization
   3 |  */
   4 |
   5 | import { test, expect } from '@playwright/test';
   6 |
   7 | test.describe('Comment Highlighting and Selection Synchronization', () => {
   8 |   test.beforeEach(async ({ page }) => {
   9 |     await page.goto('/');
   10 |     
   11 |     // Wait for the app to load
   12 |     await page.waitForSelector('body');
   13 |     
   14 |     // Sign in as a test user
   15 |     await page.fill('input[type="email"]', '<EMAIL>');
   16 |     await page.fill('input[type="password"]', 'password123');
   17 |     await page.click('button[type="submit"]');
   18 |     
   19 |     // Wait for authentication
>  20 |     await page.waitForSelector('button:has-text("New Document")', { timeout: 10000 });
      |                ^ TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.
   21 |   });
   22 |
   23 |   test('should highlight comments with user-specific colors', async ({ page }) => {
   24 |     // Create a new document
   25 |     await page.click('button:has-text("New Document")');
   26 |     await page.waitForSelector('.prose', { timeout: 5000 });
   27 |
   28 |     // Add some text to the editor
   29 |     await page.click('.prose');
   30 |     await page.type('.prose', 'This is a test document with some text for commenting.');
   31 |
   32 |     // Select text for commenting
   33 |     await page.evaluate(() => {
   34 |       const prose = document.querySelector('.prose');
   35 |       if (prose) {
   36 |         const range = document.createRange();
   37 |         const textNode = prose.firstChild;
   38 |         if (textNode) {
   39 |           range.setStart(textNode, 0);
   40 |           range.setEnd(textNode, 10); // Select "This is a "
   41 |           const selection = window.getSelection();
   42 |           selection?.removeAllRanges();
   43 |           selection?.addRange(range);
   44 |         }
   45 |       }
   46 |     });
   47 |
   48 |     // Add comment via toolbar
   49 |     await page.click('button[title="Add Comment"]');
   50 |     await page.waitForSelector('[data-testid="comment-popover"]');
   51 |     
   52 |     // Fill in comment
   53 |     await page.fill('textarea', 'This is a test comment');
   54 |     await page.click('button:has-text("Add Comment")');
   55 |     
   56 |     // Wait for comment to be created
   57 |     await page.waitForTimeout(1000);
   58 |
   59 |     // Check that the text is highlighted with user-specific styling
   60 |     const highlightedText = page.locator('.comment-highlight');
   61 |     await expect(highlightedText).toBeVisible();
   62 |     
   63 |     // Verify the highlight has user-specific data attributes
   64 |     await expect(highlightedText).toHaveAttribute('data-comment-id');
   65 |     await expect(highlightedText).toHaveAttribute('data-user-id');
   66 |     
   67 |     // Verify the highlight has inline styles for user colors
   68 |     const style = await highlightedText.getAttribute('style');
   69 |     expect(style).toContain('background-color');
   70 |     expect(style).toContain('border');
   71 |   });
   72 |
   73 |   test('should synchronize selection between editor and sidebar', async ({ page }) => {
   74 |     // Create a document with a comment (reuse setup from previous test)
   75 |     await page.click('button:has-text("New Document")');
   76 |     await page.waitForSelector('.prose', { timeout: 5000 });
   77 |     await page.click('.prose');
   78 |     await page.type('.prose', 'Test text for synchronization.');
   79 |
   80 |     // Select and comment on text
   81 |     await page.evaluate(() => {
   82 |       const prose = document.querySelector('.prose');
   83 |       if (prose) {
   84 |         const range = document.createRange();
   85 |         const textNode = prose.firstChild;
   86 |         if (textNode) {
   87 |           range.setStart(textNode, 0);
   88 |           range.setEnd(textNode, 4); // Select "Test"
   89 |           const selection = window.getSelection();
   90 |           selection?.removeAllRanges();
   91 |           selection?.addRange(range);
   92 |         }
   93 |       }
   94 |     });
   95 |
   96 |     await page.click('button[title="Add Comment"]');
   97 |     await page.waitForSelector('[data-testid="comment-popover"]');
   98 |     await page.fill('textarea', 'Test comment for sync');
   99 |     await page.click('button:has-text("Add Comment")');
  100 |     await page.waitForTimeout(1000);
  101 |
  102 |     // Open comment sidebar
  103 |     await page.click('button[title="Toggle Comments"]');
  104 |     await page.waitForSelector('[data-testid="comment-sidebar"]');
  105 |
  106 |     // Click on the comment in the sidebar
  107 |     const commentThread = page.locator('[data-testid="comment-thread"]').first();
  108 |     await commentThread.click();
  109 |
  110 |     // Verify the comment is selected (has ring styling)
  111 |     await expect(commentThread).toHaveClass(/ring-2/);
  112 |
  113 |     // Verify the corresponding text in the editor is highlighted as selected
  114 |     const highlightedText = page.locator('.comment-highlight.comment-selected');
  115 |     await expect(highlightedText).toBeVisible();
  116 |   });
  117 |
  118 |   test('should handle multiple comments on same text', async ({ page }) => {
  119 |     // Create document and add text
  120 |     await page.click('button:has-text("New Document")');
```