import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";
import { api } from "./_generated/api";

export const createDocument = mutation({
  args: {
    title: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("You must be signed in to create documents");
    }

    // Check if user has permission to create documents using the same logic as the query
    const permissionCheck = await ctx.runQuery(api.documents.canCreateDocuments, {});

    if (!permissionCheck.canCreate) {
      switch (permissionCheck.reason) {
        case "not_authenticated":
          throw new Error("You must be signed in to create documents");
        case "user_not_found":
          throw new Error("User account not found");
        case "anonymous_user":
          throw new Error("Anonymous users cannot create documents. Please sign up for an account.");
        default:
          throw new Error("You don't have permission to create documents");
      }
    }

    const documentId = await ctx.db.insert("documents", {
      title: args.title || "Untitled Document",
      createdBy: userId,
      isPublic: false,
    });

    return documentId;
  },
});

export const getDocument = query({
  args: { id: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const document = await ctx.db.get(args.id);
    if (!document) {
      return null;
    }

    // Check if user is the owner
    if (document.createdBy === userId) {
      return document;
    }

    // Check if document is public
    if (document.isPublic) {
      return document;
    }

    // Check if user has been shared access
    const share = await ctx.db
      .query("documentShares")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.id).eq("sharedWithUserId", userId)
      )
      .first();

    if (share) {
      return document;
    }

    return null;
  },
});

export const updateDocument = mutation({
  args: {
    id: v.id("documents"),
    title: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("You must be signed in to update documents");
    }

    const document = await ctx.db.get(args.id);
    if (!document) {
      throw new Error("Document not found");
    }

    // Check if user is the owner
    if (document.createdBy === userId) {
      // Owner can always edit
    } else {
      // Check if user has write permission
      const share = await ctx.db
        .query("documentShares")
        .withIndex("by_document_and_user", (q) =>
          q.eq("documentId", args.id).eq("sharedWithUserId", userId)
        )
        .first();

      if (!share || share.permission !== "write") {
        throw new Error("You don't have permission to edit this document");
      }
    }

    const updates: any = {};

    if (args.title !== undefined) {
      updates.title = args.title;
    }

    await ctx.db.patch(args.id, updates);
    return args.id;
  },
});

export const deleteDocument = mutation({
  args: { id: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("You must be signed in to delete documents");
    }

    const document = await ctx.db.get(args.id);
    if (!document) {
      throw new Error("Document not found");
    }

    // Only owners can delete documents
    if (document.createdBy !== userId) {
      throw new Error("Only document owners can delete documents");
    }

    // Delete all shares for this document
    const shares = await ctx.db
      .query("documentShares")
      .withIndex("by_document", (q) => q.eq("documentId", args.id))
      .collect();

    for (const share of shares) {
      await ctx.db.delete(share._id);
    }

    // Delete all invitations for this document
    const invitations = await ctx.db
      .query("documentInvitations")
      .withIndex("by_document", (q) => q.eq("documentId", args.id))
      .collect();

    for (const invitation of invitations) {
      await ctx.db.delete(invitation._id);
    }

    // Delete permission requests
    const requests = await ctx.db
      .query("permissionRequests")
      .withIndex("by_document", (q) => q.eq("documentId", args.id))
      .collect();

    for (const request of requests) {
      await ctx.db.delete(request._id);
    }

    // Delete the document
    await ctx.db.delete(args.id);
    return args.id;
  },
});

export const getUserDocuments = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Get documents owned by user
    const ownedDocuments = await ctx.db
      .query("documents")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .collect();

    // Get documents shared with user
    const shares = await ctx.db
      .query("documentShares")
      .withIndex("by_user", (q) => q.eq("sharedWithUserId", userId))
      .collect();

    const sharedDocuments = await Promise.all(
      shares.map(async (share) => {
        const document = await ctx.db.get(share.documentId);
        return document ? { ...document, permission: share.permission } : null;
      })
    );

    // Combine and format all documents
    const allDocuments = [
      ...ownedDocuments.map(doc => ({ ...doc, permission: "owner" as const })),
      ...sharedDocuments.filter(Boolean)
    ];

    // Get owner information for shared documents
    const documentsWithOwners = await Promise.all(
      allDocuments.map(async (document) => {
        if (!document) return null;
        const owner = await ctx.db.get(document.createdBy);

        return {
          ...document,
          owner: owner ? {
            name: owner.name,
            email: owner.email,
          } : null,
        };
      })
    );

    return documentsWithOwners
      .filter(Boolean)
      .sort((a, b) => b!._creationTime - a!._creationTime);
  },
});

// Check if user can create documents (used by UI)
export const canCreateDocuments = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { canCreate: false, reason: "not_authenticated" };
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      return { canCreate: false, reason: "user_not_found" };
    }

    // Anonymous users cannot create documents
    if (user.isAnonymous) {
      return { canCreate: false, reason: "anonymous_user" };
    }

    // Check if user has any documents they own (document owners can create)
    const ownedDocuments = await ctx.db
      .query("documents")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .first();

    if (ownedDocuments) {
      return { canCreate: true, reason: "document_owner" };
    }

    // Check if user has write permission on any document (users with write permission can create)
    const writeShares = await ctx.db
      .query("documentShares")
      .withIndex("by_user", (q) => q.eq("sharedWithUserId", userId))
      .filter((q) => q.eq(q.field("permission"), "write"))
      .first();

    if (writeShares) {
      return { canCreate: true, reason: "has_write_permission" };
    }

    // Check if user has any shared documents (users with any shared access can create)
    // This allows users who have been trusted with document access to create their own
    const anyShares = await ctx.db
      .query("documentShares")
      .withIndex("by_user", (q) => q.eq("sharedWithUserId", userId))
      .first();

    if (anyShares) {
      return { canCreate: true, reason: "has_shared_access" };
    }

    // Authenticated users without any document access can still create documents
    // This allows new users to get started
    return { canCreate: true, reason: "authenticated_user" };
  },
});
