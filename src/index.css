@tailwind base;
@tailwind components;
@tailwind utilities;

/* Collaborative cursor animations */
@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.collaborative-cursor {
  animation: cursor-blink 1s infinite;
  /* Prevent layout shifts */
  will-change: opacity;
  transform: translateZ(0); /* Force hardware acceleration */
}

.collaborative-selection {
  /* Smooth transitions but prevent flickering */
  transition: background-color 0.1s ease;
  /* Prevent layout shifts */
  will-change: background-color;
  transform: translateZ(0); /* Force hardware acceleration */
  /* Ensure selections don't interfere with text selection */
  pointer-events: none;
  user-select: none;
}

/* Comment highlighting styles */
.comment-highlight {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 1px 2px;
  margin: -1px -2px;
}

/* Text overflow and truncation utilities */
.text-truncate-fade {
  position: relative;
  overflow: hidden;
}

.text-truncate-fade::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 1.2em;
  background: linear-gradient(to right, transparent, var(--background));
  pointer-events: none;
}

.text-expandable {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.text-scrollable {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--muted-foreground) transparent;
}

.text-scrollable::-webkit-scrollbar {
  width: 6px;
}

.text-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.text-scrollable::-webkit-scrollbar-thumb {
  background-color: var(--muted-foreground);
  border-radius: 3px;
  opacity: 0.5;
}

.text-scrollable::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

/* Enhanced comment highlighting with user-specific colors and states */
.comment-highlight {
  /* Base styles are now applied via inline styles for user-specific colors */
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 1px 2px;
  margin: -1px -2px;
  position: relative;
  cursor: pointer;
}

.comment-highlight:hover {
  /* Hover effect will brighten the existing background */
  filter: brightness(1.1);
  transform: scale(1.01);
}

.comment-highlight.comment-selected {
  /* Selected state gets enhanced styling via inline styles */
  animation: comment-pulse 2s ease-in-out;
  /* Ensure selected comments are above other elements */
  z-index: 10 !important;
  position: relative !important;
}

.comment-highlight.comment-resolved {
  opacity: 0.7;
}

.comment-highlight.comment-resolved:hover {
  opacity: 1;
}

/* Pulse animation for selected comments */
@keyframes comment-pulse {
  0%, 100% {
    transform: scale(1.01);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.03);
    filter: brightness(1.15);
  }
}

/* Multiple comment indicator */
.comment-highlight.comment-multiple::after {
  content: attr(data-comment-count);
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  background-color: var(--primary);
  color: var(--primary-foreground);
  border-radius: 50%;
  border: 2px solid var(--background);
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Single comment indicator (smaller dot) */
.comment-highlight:not(.comment-multiple)::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 6px;
  height: 6px;
  background-color: currentColor;
  border-radius: 50%;
  border: 1px solid var(--background);
  opacity: 0.8;
}

/* Stacked effect for multiple comments */
.comment-highlight.comment-multiple {
  box-shadow:
    2px 2px 0 rgba(0, 0, 0, 0.1),
    4px 4px 0 rgba(0, 0, 0, 0.05);
}

/* Focus state for accessibility */
.comment-highlight:focus {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      "Inter Variable",
      ui-sans-serif,
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      "Helvetica Neue",
      Arial,
      "Noto Sans",
      sans-serif,
      "Apple Color Emoji",
      "Segoe UI Emoji",
      "Segoe UI Symbol",
      "Noto Color Emoji";
  }
}

/* only use this to update the style of the auth input fields. use a different class for all other input fields */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-md bg-background border border-input focus:border-ring focus:ring-1 focus:ring-ring outline-none transition-shadow shadow-sm hover:shadow;
}

/* only use this to update the style of the auth buttons. use the button class for all other buttons */
.auth-button {
  @apply w-full px-4 py-3 rounded-md bg-primary text-primary-foreground font-semibold hover:bg-primary/90 transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
}
