/**
 * URL routing utilities for document navigation
 * Handles document ID parsing, validation, and URL generation
 */

import { Id } from "../../convex/_generated/dataModel";

/**
 * Validates if a string is a valid Convex document ID format
 * Convex IDs are strings at runtime, typically in format like "j57abc123def456"
 */
export function isValidDocumentId(id: string): boolean {
  // Basic validation - Convex IDs are non-empty strings
  // More specific validation could be added based on Convex ID patterns
  // Exclude common route names that shouldn't be treated as document IDs
  const excludedRoutes = ['other', 'about', 'settings', 'profile', 'admin', 'api'];
  return typeof id === "string" &&
         id.length > 0 &&
         !id.includes("/") &&
         !id.includes("?") &&
         !excludedRoutes.includes(id.toLowerCase());
}

/**
 * Extracts document ID from URL pathname
 * Supports patterns: /document/{documentId} only for better URL structure
 */
export function extractDocumentIdFromPath(pathname: string): string | null {
  // Remove leading slash
  const cleanPath = pathname.startsWith("/") ? pathname.slice(1) : pathname;

  // Handle /document/{documentId} pattern only
  if (cleanPath.startsWith("document/")) {
    const documentId = cleanPath.slice("document/".length);
    return isValidDocumentId(documentId) ? documentId : null;
  }

  return null;
}

/**
 * Extracts document ID from URL search params
 * Supports pattern: ?doc={documentId}
 */
export function extractDocumentIdFromSearch(search: string): string | null {
  const params = new URLSearchParams(search);
  const docId = params.get("doc");
  
  if (docId && isValidDocumentId(docId)) {
    return docId;
  }
  
  return null;
}

/**
 * Extracts document ID from full URL (pathname or search params)
 * Tries pathname first, then falls back to search params
 */
export function extractDocumentIdFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url, window.location.origin);
    
    // Try pathname first
    const pathId = extractDocumentIdFromPath(urlObj.pathname);
    if (pathId) {
      return pathId;
    }
    
    // Fall back to search params
    return extractDocumentIdFromSearch(urlObj.search);
  } catch {
    // If URL parsing fails, try as pathname only
    return extractDocumentIdFromPath(url);
  }
}

/**
 * Generates URL for a document using the /document/{documentId} pattern
 */
export function generateDocumentUrl(documentId: Id<"documents">): string {
  return `/document/${documentId}`;
}

/**
 * Generates URL for the home page (no document selected)
 */
export function generateHomeUrl(): string {
  return "/";
}

/**
 * Checks if the current URL represents a document route
 */
export function isDocumentRoute(pathname: string): boolean {
  return extractDocumentIdFromPath(pathname) !== null;
}

/**
 * Navigation utilities for programmatic routing
 */
export const navigationUtils = {
  /**
   * Navigate to a document without page refresh
   */
  navigateToDocument: (documentId: Id<"documents">) => {
    const url = generateDocumentUrl(documentId);
    window.history.pushState(null, "", url);
  },
  
  /**
   * Navigate to home without page refresh
   */
  navigateToHome: () => {
    const url = generateHomeUrl();
    window.history.pushState(null, "", url);
  },
  
  /**
   * Replace current URL without adding to history
   */
  replaceUrl: (url: string) => {
    window.history.replaceState(null, "", url);
  }
};
